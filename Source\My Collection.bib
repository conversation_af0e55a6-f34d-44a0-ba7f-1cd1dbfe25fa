@article{Yeng2020,
abstract = {Healthcare organizations consist of unique activities including collaborating on patients care and emergency care. The sector also accumulates high sensitive multifaceted patients' data such as text reports, radiology images and pathological slides. The large volume of the data is often stored as Electronic Health Records (EHR) which must be frequently updated while ensuring higher percentage up-time for constant availability of patients' records. Healthcare as a critical infrastructure also needs highly skilled IT personnel, Information and Communication Technology (ICT) and infrastructure with regular maintenance culture. Fortunately, cloud computing can provide these necessary services at a lower cost. But with all thees enormous benefits of cloud computing, it is characterized with various information security issues which is not enticing to healthcare. Amid many threat modelling methods, which of them is suitable for identifying cloud related threats towards the adoption of cloud computing for healthcare? This paper compared threat modelling methods to determine their suitability for identifying and managing healthcare related threats in cloud computing. Threat modelling in pervasive computing (TMP) was identified to be suitable and can be combined with Attack Tree (AT), Attack Graph (AG) and Practical Threat Analysis (PTA) or STRIDE (spoofing, tampering, repudiation, information disclosure, denial of service and elevation of privilege). Also Attack Tree (AT) could be complemented with TMP, AG and STRIDE or PTA. Healthcare IT security professionals can hence rely on these methods in their security practices, to identify cloud related threats for healthcare. Essentially, privacy related threat modeling methods such as LINDDUN framework, need to be included in these synergy of cloud related threat modelling methods towards enhancing security and privacy for healthcare needs.},
author = {Yeng, <PERSON><PERSON>. and <PERSON>lthusen, <PERSON>. and <PERSON>, Bian},
doi = {10.14569/IJACSA.2020.0111194},
file = {:D\:/Docs/Source/202006L009.pdf:pdf},
isbn = {9789898704153},
issn = {21565570},
journal = {International Journal of Advanced Computer Science and Applications},
keywords = {Cloud computing,data privacy,healthcare,security practice,threat modelling},
number = {11},
pages = {772--784},
title = {{Comparative Analysis of Threat Modeling Methods for Cloud Computing towards Healthcare Security Practice}},
volume = {11},
year = {2020}
}
@article{Husak2021,
abstract = {Predictive analysis allows next-generation cyber defense that is more proactive than current approaches based on intrusion detection. In this paper, we discuss various aspects of predictive methods in cyber defense and illustrate them on three examples of recent approaches. The first approach uses data mining to extract frequent attack scenarios and uses them to project ongoing cyberattacks. The second approach uses a dynamic network entity reputation score to predict malicious actors. The third approach uses time series analysis to forecast attack rates in the network. This paper presents a unique evaluation of the three distinct methods in a common environment of an intrusion detection alert sharing platform, which allows for a comparison of the approaches and illustrates the capabilities of predictive analysis for current and future research and cybersecurity operations. Our experiments show that all three methods achieved a sufficient technology readiness level for experimental deployment in an operational setting with promising accuracy and usability. Namely prediction and projection methods, despite their differences, are highly usable for predictive blacklisting, the first provides a more detailed output, and the second is more extensible. Network security situation forecasting is lightweight and displays very high accuracy, but does not provide details on predicted events.},
author = {Hus{\'{a}}k, Martin and Barto{\v{s}}, V{\'{a}}clav and Sokol, Pavol and Gajdo{\v{s}}, Andrej},
doi = {10.1016/j.future.2020.10.006},
file = {:D\:/Docs/Source/2021-FGCS-Predictive-methods-in-cyber-defense.pdf:pdf},
issn = {0167739X},
journal = {Future Generation Computer Systems},
keywords = {Cybersecurity,Data mining,Forecasting,Machine learning,Prediction,Time series},
number = {October},
pages = {517--530},
title = {{Predictive methods in cyber defense: Current experience and research challenges}},
volume = {115},
year = {2021}
}
@article{Alazmi2022,
abstract = {Web applications have been a significant target for successful security breaches in the last few years. They are currently secured, as a primary method, by searching for their vulnerabilities with specialized tools referred to as Web Application Vulnerability Scanners (WVS's). Although, these dynamic approaches of testing have some advantages, there is still a scarcity of studies that explore their features and detection capabilities in a systematic way. This article reports findings from a Systematic Literature Review (SLR) to look into the characteristics and effectiveness of the most frequently used WVS's. A total of 90 research papers were carefully evaluated. Thirty (30) WVS's were collected and reported, with only 12 having at least one quantitative assessment of effectiveness. These 12 WVS's were evaluated by 15 original evaluation studies. We found that these evaluations tested mostly only two of the Open Web Application Security Project (OWASP) Top Ten vulnerability types: SQL injection (SQLi) (13/15) and Cross-Site Scripting (XSS) (8/15). Additionally, only one work evaluated six of the OWASP Top Ten vulnerability types and for only one scanner. We also found that the reported detection rates were highly dissimilar between these 15 evaluations. Based on these surprising results we suggest avenues for future directions.},
author = {Alazmi, Suliman and {De Leon}, Daniel Conte},
doi = {10.1109/ACCESS.2022.3161522},
file = {:D\:/Docs/Source/A_Systematic_Literature_Review_on_the_Characterist.pdf:pdf},
issn = {21693536},
journal = {IEEE Access},
keywords = {OWASP top ten,Web applications,black-box testing,detection rate,effectiveness and performance,web vulnerability scanner},
pages = {33200--33219},
publisher = {IEEE},
title = {{A Systematic Literature Review on the Characteristics and Effectiveness of Web Application Vulnerability Scanners}},
volume = {10},
year = {2022}
}
@article{Almahmoud2025,
abstract = {Geopolitical instability is exacerbating the risk of catastrophic cyber-attacks striking where defences are weak. Nevertheless, cyber-attack trend forecasting predominantly relies on human expertise, which is susceptible to subjectivity and potential bias. As a solution, we have recently presented a novel study that harnesses machine learning for long-term cyber-attack forecasting. Building upon this groundwork, our research advances to the next level, by predicting the disparity between cyber-attack trends and the trend of the relevant alleviation technologies. The proposed approach applies key constructs of Protection Motivation Theory while introducing a proactive version of the theory. Our predictive analysis aims to offer strategic insights for the decision of investment in cyber security technologies. It also provides a sound foundation for the strategic decisions of national defence agencies. To achieve this objective, we have expanded our dataset, which now encompasses records spanning 42 distinct cyber-attack types and various related features, alongside data concerning the trends of 98 pertinent technologies, dating back to 2011. The dataset features were meticulously curated from diverse sources, including news articles, blogs, government advisories, as well as from platforms such as Elsevier, Twitter, and Python APIs. With our comprehensive dataset in place, we construct a graph that elucidates the intricate interplay between cyber threats and the development of pertinent alleviation technologies. To forecast the graph, we introduce a novel Bayesian adaptation of a recently proposed graph neural network model, which effectively captures and predicts these trends. We further demonstrate the efficacy of our proposed features in this context. Furthermore, our study extends its horizon by generating future data projections for the next three years, encompassing forecasts for the evolving graph, including predictions of the gap between cyber-attack trends and the trend of the associated technologies. As a consequential outcome of our forecasting efforts, we introduce the concept of “alleviation technologies cycle”, delineating the key phases in the life cycle of 98 technologies. These findings serve as a foundational resource, offering valuable guidance for future investment and strategic defence decisions within the realm of cyber security related technologies.},
author = {Almahmoud, Zaid and Yoo, Paul D. and Damiani, Ernesto and Choo, Kim Kwang Raymond and Yeun, Chan Yeob},
doi = {10.1016/j.techfore.2024.123836},
file = {:D\:/Docs/Source/Almahmoud, Z., Yoo, P., Damiani, E., Choo, K.-K. R., & Yeun, C. (2025). Forecasting cyber threats and pertinent mitigation technologies. Technological Forecasting and Social Change..pdf:pdf},
issn = {00401625},
journal = {Technological Forecasting and Social Change},
keywords = {Alleviation technology trend forecasting,Big data analytics,Cyber threat trend forecasting,Graph machine learning,Mitigation technology trend forecasting,Proactive approach,Technology cycle},
number = {October 2024},
pages = {123836},
publisher = {Elsevier Inc.},
title = {{Forecasting Cyber Threats and Pertinent Mitigation Technologies}},
url = {https://doi.org/10.1016/j.techfore.2024.123836},
volume = {210},
year = {2025}
}
@article{Anupam2020,
abstract = {Today, web developers don't write the entire code from scratch, rather they rely on many open source components. The widespread use means developers don't have an in-depth knowledge of how the code exactly works. Often these components are not kept up to date with the latest release and patches. These unmaintained versions add to the security threats. One way of keeping a track of such dependencies is to continuously monitor them with scanning tools. Out of the few open source tools available, Retire.js and Snyk are among the top. Retire.js, is recommended by OWASP (Open Web Application Security Project) for scanning node and JavaScript vulnerabilities. Snyk on the other hand is more feature rich than most other tools and has an extensive vulnerability database. Both these tools come with CLI (command line interface) integration, which provides a benchmark standard for comparison. This paper reviews the tools for both feature-based comparisons (based on existing features) as well as result-based comparisons (based on scanning result). Feature-based comparisons will focus on parameters like user-friendliness, vulnerability database, extent of scanning and features of command line interface of two tools. Result-based comparison will directly compare the result of scans and the vulnerability database of two tools.},
author = {Anupam, Aman and Gonchigar, Prathika and Sharma, Shashank},
file = {:D\:/Docs/Source/Anupam, A., Gonchigar, P., Sharma, S., Prof. Prapulla, S., & Anala. (2020). Analysis of open source Node.js vulnerability scanners. International Journal of Computer Applications, 175(4), 1–8..pdf:pdf},
issn = {2395-0072},
journal = {International Research Journal of Engineering and Technology},
keywords = {Components with known vulnerabilities,OWASP Top 10,Retirejs,Snyk,Vulnerability Scanner,Web Security},
number = {May},
pages = {5449--5454},
title = {{Analysis of Open Source Node.js Vulnerability Scanners}},
url = {www.irjet.net},
year = {2020}
}
@article{Chakraborty2022,
abstract = {Automated detection of software vulnerabilities is a fundamental problem in software security. Existing program analysis techniques either suffer from high false positives or false negatives. Recent progress in Deep Learning (DL) has resulted in a surge of interest in applying DL for automated vulnerability detection. Several recent studies have demonstrated promising results achieving an accuracy of up to 95 percent at detecting vulnerabilities. In this paper, we ask, 'how well do the state-of-the-art DL-based techniques perform in a real-world vulnerability prediction scenario?' To our surprise, we find that their performance drops by more than 50 percent. A systematic investigation of what causes such precipitous performance drop reveals that existing DL-based vulnerability prediction approaches suffer from challenges with the training data (e.g., data duplication, unrealistic distribution of vulnerable classes, etc.) and with the model choices (e.g., simple token-based models). As a result, these approaches often do not learn features related to the actual cause of the vulnerabilities. Instead, they learn unrelated artifacts from the dataset (e.g., specific variable/function names, etc.). Leveraging these empirical findings, we demonstrate how a more principled approach to data collection and model design, based on realistic settings of vulnerability prediction, can lead to better solutions. The resulting tools perform significantly better than the studied baseline-up to 33.57 percent boost in precision and 128.38 percent boost in recall compared to the best performing model in the literature. Overall, this paper elucidates existing DL-based vulnerability prediction systems' potential issues and draws a roadmap for future DL-based vulnerability prediction research.},
archivePrefix = {arXiv},
arxivId = {2009.07235},
author = {Chakraborty, Saikat and Krishna, Rahul and Ding, Yangruibo and Ray, Baishakhi},
doi = {10.1109/TSE.2021.3087402},
eprint = {2009.07235},
file = {:D\:/Docs/Source/chakraborty2021.pdf:pdf},
issn = {19393520},
journal = {IEEE Transactions on Software Engineering},
keywords = {Vulnerability,deep learning based vulnerability detection,graph neural network based vulnerability detection,real world vulnerabilities},
number = {9},
pages = {3280--3296},
title = {{Deep Learning Based Vulnerability Detection: Are We There Yet?}},
volume = {48},
year = {2022}
}
@article{KiranGandikota2023,
abstract = {In current information era, every small-scale industry, MNC's, Schools, and Colleges utilise Web Applications to promote their organizations and provide services to society. Web Applications have become a simple and vital medium for communicating with society.This leads to an increase in website utilization. Threats and cyber-attacks are increasing according to the rate of growth of Web Applications. Any organization must ensure the privacy and security of its users' data in order to maintain the company's and users' integrity.Web application vulnerabilities represent major security risks, making it critical to conduct extensive assessments and penetration testing to discover and remediate such flaws. The paper provides a complete overview of web application vulnerability assessment and penetration testing, emphasizing the need of proactive security measures in protecting sensitive data and preserving application integrity. This study aims to identify and categorize web application vulnerabilities (OWASPs) that may compromise the security of the application and result in user data breaches. The primary focus of this project is the security and privacy of user data. Integrating security practices into the software development life cycle through investigating real-world case studies and industry standards. Ultimately, our research adds to the growing body of knowledge in web application security, assisting organizations in creating resilient defenses against cyber threats.},
author = {{Kiran Gandikota}, Prasanth Satya Sai and Valluri, Deekshitha and Mundru, Sathvik Babu and Yanala, Gopi Krishna and Sushaini, S.},
doi = {10.1016/j.procs.2023.12.072},
file = {:D\:/Docs/Source/Gandikota, P. S. S. K., Rao, C. T., & Singh, A. (2023). Web application security through comprehensive vulnerability assessment. Procedia Computer Science, 197, 123–130..pdf:pdf},
issn = {18770509},
journal = {Procedia Computer Science},
keywords = {MNC's,Web Applications,cyber threats,integrity,penetration testing},
number = {2023},
pages = {168--182},
publisher = {Elsevier B.V.},
title = {{Web Application Security through Comprehensive Vulnerability Assessment}},
url = {https://doi.org/10.1016/j.procs.2023.12.072},
volume = {230},
year = {2023}
}
@article{Jacobs2020,
abstract = {Despite significant innovations in IT security products and research over the past 20 years, the information security field is still immature and struggling. Practitioners lack the ability to properly assess cyber risk, and decision-makers continue to be paralyzed by vulnerability scanners that overload their staff with mountains of scan results. In order to cope, firms prioritize vulnerability remediation using crude heuristics and limited data, though they are still too often breached by known vulnerabilities for which patches have existed for months or years. And so, the key challenge firms face is trying to identify a remediation strategy that best balances two competing forces. On one hand, it could attempt to patch all vulnerabilities on its network. While this would provide the greatest 'coverage' of vulnerabilities patched, it would inefficiently consume resources by fixing low-risk vulnerabilities. On the other hand, patching a few high-risk vulnerabilities would be highly 'efficient', but may leave the firm exposed to many other high-risk vulnerabilities. Using a large collection of multiple datasets together with machine learning techniques, we construct a series of vulnerability remediation strategies and compare how each perform in regard to trading off coverage and efficiency. We expand and improve upon the small body of literature that uses predictions of 'published exploits', by instead using 'exploits in the wild' as our outcome variable. We implement the machine learning models by classifying vulnerabilities according to high- and low-risk, where we consider high-risk vulnerabilities to be those that have been exploited in actual firm networks.},
author = {Jacobs, Jay and Romanosky, Sasha and Adjerid, Idris and Baker, Wade},
doi = {10.1093/CYBSEC/TYAA015},
file = {:D\:/Docs/Source/Improving_vulnerability_remediation_through_better.pdf:pdf},
issn = {20572093},
journal = {Journal of Cybersecurity},
keywords = {CVSS,Exploited vulnerability,Machine learning,Precision,Recall,Security risk management,Vulnerability management},
number = {1},
pages = {1--12},
title = {{Improving vulnerability remediation through better exploit prediction}},
volume = {6},
year = {2020}
}
@article{Hussain2024,
abstract = {For the past few years, software security has become a pressing issue that needs to be addressed during software development. In practice, software security is considered after the deployment of software rather than considered as an initial requirement. This delayed action leads to security vulnerabilities that can be catered for during the early stages of the software development life cycle (SDLC). To safeguard a software product from security vulnerabilities, security must be given equal importance with functional requirements during all phases of SDLC. In this paper, we propose a policy‐driven waterfall model (PDWM) for secure software development describing key points related to security aspects in the software development process. The security requirements are the security policies that are considered during all phases of waterfall‐based SDLC. A framework of PDWM is presented and applied to the e‐travel scenario to ascertain its effectiveness. This scenario is a case of small to medium‐sized software development project. The results of case study show that PDWM can identify 33% more security vulnerabilities as compared to other secure software development techniques.},
author = {Hussain, Shariq and Anwaar, Haris and Sultan, Kashif and Mahmud, Umar and Farooqui, Sherjeel and Karamat, Tehmina and Toure, Ibrahima Kalil},
doi = {10.1155/2024/9962691},
file = {:D\:/Docs/Source/Journal of Engineering - 2024 - Hussain - Mitigating Software Vulnerabilities through Secure Software Development with a.pdf:pdf},
issn = {2314-4904},
journal = {Journal of Engineering},
number = {1},
title = {{Mitigating Software Vulnerabilities through Secure Software Development with a Policy‐Driven Waterfall Model}},
volume = {2024},
year = {2024}
}
@article{Khan2025,
abstract = {With the increasing reliance on software applications, cybersecurity threats have become a critical concern for developers and organizations. The answer to this vulnerability is AI systems, which help us adapt a little better, as traditional measures in security have failed to respond to the upcoming threats. This paper presents an innovative cybersecurity framework using AI, by the Artificial Neural Network (ANN)—Interpretive Structural Modeling (ISM) model, to improve threat detection, vulnerability assessment, and risk response during software development. This framework helps realize dynamic, intelligent security as a part of the Software Development life cycle (SDLC). Initially, existing cybersecurity risks in software coding are systematically evaluated to identify potential gaps and integrate best practices into the proposed model. In the second phase, an empirical survey was conducted to identify and validate the findings of the systematic literature review (SLR). In the third phase, a hybrid approach is employed, integrating ANN for real-time threat detection and risk assessment. It utilizes ISM to analyze the relationships between cybersecurity risks and vulnerabilities, creating a structured framework for understanding interdependencies. A case study was conducted in the last stage to test and evaluate the AI-driven cybersecurity Mitigation Model for Secure Software Coding. A multi-level categorization system is also used to assess maturity across five key levels: Ad hoc, Planned, Standardized, Metrics-Driven, and Continuous Improvements. This study identifies 15 cybersecurity risks and vulnerabilities in software coding, along with 158 AI-driven best practices for mitigating these risks. It also identifies critical areas of insecure coding practices and develops a scalable model to address cybersecurity risks across different maturity levels. The results show that AI outperforms traditional systems in detecting security weaknesses and simultaneously fixing problems. During Levels 1–3 of the system improvement process, advanced security methods are used to protect against threats. Our analysis reveals that organizations at Levels 4 and 5 still need to entirely shift to using AI-based protection tools and techniques. The proposed system provides developers and managers with valuable insights, enabling them to select security enhancements tailored to their organization's development stages. It supports automated threat analysis, helping organizations stay vigilant against potential cybersecurity threats. The study introduces a novel ANN-ISM framework integrating AI tools with cybersecurity modeling formalisms. By merging AI systems with secure software coding principles, this research enhances the connection between AI-generated insights and real-world cybersecurity usage.},
author = {Khan, Habib Ullah and Khan, Rafiq Ahmad and Alwageed, Hathal S. and Almagrabi, Alaa Omran and Ayouni, Sarra and Maddeh, Mohamed},
doi = {10.1038/s41598-025-97204-y},
file = {:D\:/Docs/Source/Khan, H. U., Khan, R., Alwageed, H., Almagrabi, A., Ayouni, S., & Maddeh, M. (2025). AI‑driven cybersecurity framework for software development based on the ANN‑ISM paradigm. Scientific Reports, 15, 97204..pdf:pdf},
issn = {20452322},
journal = {Scientific Reports},
keywords = {AI,ANN-ISM modeling,Case study,Cybersecurity maturity levels,Cybersecurity risks and practices,Empirical survey,Secure software coding,Systematic literature review},
month = {apr},
number = {1},
pages = {13423},
pmid = {40251237},
title = {{AI-driven cybersecurity framework for software development based on the ANN-ISM paradigm}},
url = {https://www.nature.com/articles/s41598-025-97204-y},
volume = {15},
year = {2025}
}
@article{,
abstract = {The rise in sophisticated cyber threats demands advanced cybersecurity methods that surpass traditional rule-based approaches. This study explores the application of Artificial Intelligence (AI) to enhance predictive cybersecurity, enabling more accurate threat forecasting and effective vulnerability management. The research assesses various AI modelssuch as neural networks, decision trees, and Support Vector Machines (SVMs) in their ability to predict cyber threats. Employing a quantitative methodology, the study utilizes historical data from cybersecurity sources, threat intelligence feeds, vulnerability logs, and incident reports. Key performance metrics, including accuracy, precision, recall, F1-score, and Receiver Operating Characteristic - Area Under the Curve (ROC-AUC), were used to test, validate, and train the AI models. Neural networks emerged as the most accurate, achieving 93% accuracy, particularly excelling in identifying phishing attacks and zero-day vulnerabilities. SVM models also performed well, minimizing false positives and increasing detection rates, while decision trees proved computationally efficient and easily interpretable in simpler cybersecurity scenarios. The findings underscore the superiority of AI models over traditional methods, offering dynamic solutions for evolving cyber threats. This research contributes to the field by demonstrating the extensive potential of AI in predictive cybersecurity, providing actionable insights for organizations implementing AI-driven threat detection and vulnerability management.},
author = { and  and  and  and  and  and  and Egho-Promise, Ehigiator and Asante, George and Balisane, Hewa and Salih, Abdulrahman and Aina, Folayo and Kure, Halima and Gavua, Ebenezer},
doi = {10.26562/ijirae.2025.v1202.01},
file = {:D\:/Docs/Source/LeveragingAIforPredictivecybersecurity.pdf:pdf},
journal = {International Journal of Innovative Research in Advanced Engineering},
number = {02},
pages = {68--79},
title = {{Leveraging Artificial Intelligence for Predictive CyberSecurity: Enhancing Threat Forecasting and Vulnerability Management}},
volume = {12},
year = {2024}
}
@article{Kalouptsoglou2023,
abstract = {Context: Software security is considered a major aspect of software quality as the number of discovered vulnerabilities in software products is growing. Vulnerability prediction is a mechanism that helps engineers to prioritize their inspection efforts focusing on vulnerable parts. Despite the recent advancements, current literature lacks a systematic mapping study on vulnerability prediction. Objective: This paper aims to analyze the state-of-the-art of vulnerability prediction focusing on: (a) the goals of vulnerability prediction-related studies; (b) the data collection processes and the types of datasets that exist in the literature; (c) the mostly examined techniques for the construction of the prediction models and their input features; and (d) the utilized evaluation techniques. Method: We collected 180 primary studies following a broad search methodology across four popular digital libraries. We mapped these studies to the variables of interest and we identified trends and relationships between the studies. Results: The main findings suggest that: (i) there are two major study types, prediction of vulnerable software components and forecasting of the evolution of vulnerabilities in software; (ii) most studies construct their own vulnerability-related dataset retrieving information from vulnerability databases for real-world software; (iii) there is a growing interest for deep learning models along with a trend on textual source code representation; and (iv) F1-score was found to be the most widely used evaluation metric. Conclusions: The results of our study indicate that there are several open challenges in the domain of vulnerability prediction. One of the major conclusions, is the fact that most studies focus on within-project prediction, neglecting the real-world scenario of cross-project prediction.},
author = {Kalouptsoglou, Ilias and Siavvas, Miltiadis and Ampatzoglou, Apostolos and Kehagias, Dionysios and Chatzigeorgiou, Alexander},
doi = {10.1016/j.infsof.2023.107303},
file = {:D\:/Docs/Source/VP_SMS_IST_RG.pdf:pdf},
issn = {09505849},
journal = {Information and Software Technology},
keywords = {Machine learning,Software security,Systematic mapping study,Vulnerability prediction},
number = {August},
title = {{Software vulnerability prediction: A systematic mapping study}},
volume = {164},
year = {2023}
}
@article{Jiang2021,
abstract = {Dynamic data-driven vulnerability assessments face massive heterogeneous data contained in, and produced by SOCs (Security Operations Centres). Manual vulnerability assessment practices result in inaccurate data and induce complex analytical reasoning. Contemporary security repositories' diversity, incompleteness and redundancy contribute to such security concerns. These issues are typical characteristics of public and manufacturer vulnerability reports, which exacerbate direct analysis to root out security deficiencies. Recent advances in machine learning techniques promise novel approaches to overcome these notorious diversity and incompleteness issues across massively increasing vulnerability reports corpora. Yet, these techniques themselves exhibit varying degrees of performance as a result of their diverse methods. We propose a cognitive cybersecurity approach that empowers human cognitive capital along two dimensions. We first resolve conflicting vulnerability reports and preprocess embedded security indicators into reliable data sets. Then, we use these data sets as a base for our proposed ensemble meta-classifier methods that fuse machine learning techniques to improve the predictive accuracy over individual machine learning algorithms. The application and implication of this methodology in the context of vulnerability analysis of computer systems are yet to unfold the full extent of its potential. The proposed cognitive security methodology in this paper is shown to improve performances when addressing the above-mentioned incompleteness and diversity issues across cybersecurity alert repositories. The experimental analysis conducted on actual cybersecurity data sources reveals interesting tradeoffs of our proposed selective ensemble methodology, to infer patterns of computer system vulnerabilities.},
author = {Jiang, Yuning and Atif, Yacine},
doi = {10.1016/j.jnca.2021.103210},
file = {:D\:/Docs/Source/Jiang, Y., & Atif, Y. (2021). A selective ensemble model for cognitive cybersecurity analysis. Journal of Network and Computer Applications, 193, 103210..pdf:pdf},
issn = {10848045},
journal = {Journal of Network and Computer Applications},
keywords = {Data correlation,Data mining,Database management,Ensemble,Information security,Machine learning,Vulnerability analysis},
month = {nov},
number = {June 2020},
pages = {103210},
publisher = {Elsevier Ltd},
title = {{A selective ensemble model for cognitive cybersecurity analysis}},
url = {https://doi.org/10.1016/j.jnca.2021.103210 https://linkinghub.elsevier.com/retrieve/pii/S1084804521002125},
volume = {193},
year = {2021}
}
@article{Jiang2022,
abstract = {Despite their wide proliferation, complex cyber-physical systems (CPSs) are subject to cybersecurity vulnerabilities and potential attacks. Vulnerability assessment for such complex systems are challenging, partly due to the discrepancy among mechanisms used to evaluate their cyber-security weakness levels. Several sources do report these weaknesses like the National Vulnerability Database (NVD), as well as manufacturer websites besides other security scanning advisories such as Cyber Emergency Response Team (CERT), VulDB, and Shodan databases. However, these multiple sources are found to face inconsistency issues, especially in terms of vulnerability severity scores. We advocate an artificial intelligence based approach to streamline the computation of vulnerability severity magnitudes. This approach decreases the error rate induced by manual calculation processes, that are tradi- tionally used in cybersecurity analysis. Popular repositories such as NVD and SecurityFocus are employed to validate the proposed approach. In doing so, we report discovered correlations among reported vulnerability scores to infer consistent magnitude values of vulnerability instances. The method is applied to a case study featuring a CPS application to illustrate the automation of the proposed vulnerability scoring mechanism, used to mitigate cybersecurity weaknesses.},
author = {Jiang, Yuning and Atif, Yacine},
doi = {10.2139/ssrn.4019226},
file = {:D\:/Docs/Source/Jiang, Y., & Atif, Y. (2022). Towards automatic discovery and assessment of vulnerability severity in cyber‑physical systems. Array, 15, 100209..pdf:pdf},
issn = {1556-5068},
journal = {SSRN Electronic Journal},
keywords = {Data correlation,Data mining,Database management,Ensemble,Information security,Machine learning,Vulnerability analysis},
month = {nov},
pages = {103210},
title = {{Towards Automatic Discovery and Assessment of Vulnerability Severity in Cyber-Physical Systems}},
url = {https://linkinghub.elsevier.com/retrieve/pii/S1084804521002125 https://www.ssrn.com/abstract=4019226},
volume = {193},
year = {2022}
}
